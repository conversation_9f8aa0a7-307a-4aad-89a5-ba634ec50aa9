import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, filter, take, switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';

import { AuthService } from '../../Pages/authentication/Services/auth.service';
import { AuthStateService } from '../../Pages/authentication/Services/AuthState.service';
import { AuthErrorHandlerService } from '../../Pages/authentication/Services/auth-error-handler.service';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null,
  );

  constructor(
    private authService: AuthService,
    private authState: AuthStateService,
    private router: Router,
    private authErrorHandler: AuthErrorHandlerService,
  ) {}

  intercept(
    request: HttpRequest<unknown>,
    next: HttpHandler,
  ): Observable<HttpEvent<unknown>> {
    // Add auth header with jwt if user is logged in and request is to api url
    const token = this.authService.getToken();
    const isApiUrl = this.isApiRequest(request.url);

    if (token && isApiUrl) {
      request = this.addTokenHeader(request, token);
    }

    return next.handle(request).pipe(
      catchError((error) => {
        if (error instanceof HttpErrorResponse && isApiUrl) {
          return this.handleHttpError(error, request, next);
        }
        return throwError(() => error);
      }),
    );
  }

  private addTokenHeader(
    request: HttpRequest<any>,
    token: string,
  ): HttpRequest<any> {
    return request.clone({
      headers: request.headers.set('Authorization', `Bearer ${token}`),
    });
  }

  private isApiRequest(url: string): boolean {
    // Check if the request is to your API
    // Updated to include the new port 5001
    const isApi =
      url.includes('/api/') ||
      url.includes('localhost:5000') ||
      url.includes('localhost:5001') ||
      url.includes('your-api-domain.com');

    return isApi;
  }

  /**
   * Centralized HTTP error handling for authentication requests
   */
  private handleHttpError(
    error: HttpErrorResponse,
    request: HttpRequest<any>,
    next: HttpHandler,
  ): Observable<HttpEvent<any>> {
    switch (error.status) {
      case 401:
        return this.handle401Error(request, next, error);
      case 403:
        this.authErrorHandler.handleAuthError(error);
        return throwError(() => error);
      case 400:
      case 404:
      case 500:
      case 0:
        this.authErrorHandler.handleAuthError(error);
        return throwError(() => error);
      default:
        this.authErrorHandler.handleAuthError(error);
        return throwError(() => error);
    }
  }

  /**
   * Handle 401 Unauthorized errors with token refresh logic
   */
  private handle401Error(
    request: HttpRequest<any>,
    next: HttpHandler,
    error: HttpErrorResponse,
  ): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      // Check if token is expired
      if (this.authService.isTokenExpired()) {
        // Token is expired, logout user
        this.isRefreshing = false;
        this.authService.logout();
        this.router.navigate(['/auth/login']);
        this.authErrorHandler.handleAuthError(error);
        return throwError(() => error);
      }

      // If we have a valid token but got 401, it might be a server issue
      // For now, just logout the user
      this.isRefreshing = false;
      this.authService.logout();
      this.router.navigate(['/auth/login']);
      this.authErrorHandler.handleAuthError(error);
      return throwError(() => error);
    }

    // If we're already refreshing, wait for the new token
    return this.refreshTokenSubject.pipe(
      filter((token) => token !== null),
      take(1),
      switchMap((token) => next.handle(this.addTokenHeader(request, token))),
    );
  }
}
