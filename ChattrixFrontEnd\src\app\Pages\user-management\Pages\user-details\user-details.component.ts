import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { UserManagementService } from '../../Services/UserManagement.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import { UserProfileService } from '../../../../Core/Services/UserProfile.service';
import { UserDetails } from '../../Models/UserManagement';
import { ComponentBaseUtil } from '../../../../Core/Utils/component-base.util';

@Component({
  selector: 'app-user-details',
  standalone: false,
  templateUrl: './user-details.component.html',
  styleUrl: './user-details.component.scss',
})
export class UserDetailsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  user: UserDetails | null = null;
  userId: string | null = null;
  isLoading = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private userManagementService: UserManagementService,
    private componentBaseUtil: ComponentBaseUtil,
    private userProfileService: UserProfileService,
  ) {}

  ngOnInit(): void {
    this.loadUserFromRoute();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadUserFromRoute(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    if (this.userId) {
      this.loadUserDetails(this.userId);
    } else {
      this.navigateToUserList();
    }
  }

  private loadUserDetails(userId: string): void {
    this.isLoading = true;
    this.userManagementService
      .getUserById(userId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (user: UserDetails) => {
          this.user = user;
          this.isLoading = false;
        },
        error: (error) => {
          this.componentBaseUtil.showError(
            this.componentBaseUtil.extractErrorMessage(error),
          );
          this.isLoading = false;
          this.user = null;
        },
      });
  }

  /**
   * Get profile picture URL for a user
   */
  getProfilePictureUrl(user: UserDetails): string | undefined {
    if (!user.profileImageUrl) {
      return undefined;
    }

    // If it's already a full URL, return as is
    if (user.profileImageUrl.startsWith('http')) {
      return user.profileImageUrl;
    }

    // Otherwise, construct the full S3 URL using UserProfileService
    return this.userProfileService.getS3Url(user.profileImageUrl);
  }

  /**
   * Get user initials for avatar fallback
   */
  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  formatDate(date: Date): string {
    if (!date) return 'N/A';
    const dateObj = new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  getRoleClass(role: string): string {
    const normalizedRole = role.toLowerCase().replace(' ', '-');
    return `role-${normalizedRole}`;
  }

  getRoleDisplayName(role: string): string {
    return role.charAt(0).toUpperCase() + role.slice(1);
  }

  getRoleIcon(role: string): string {
    const roleIcons = {
      User: 'person',
      Admin: 'admin_panel_settings',
      'Super Admin': 'supervisor_account',
    };
    return roleIcons[role as keyof typeof roleIcons] || 'person';
  }

  onEditUser(): void {
    if (this.user?.id) {
      this.router.navigate(['/user-management/edit', this.user.id]);
    }
  }

  onBackToList(): void {
    this.navigateToUserList();
  }

  private navigateToUserList(): void {
    this.router.navigate(['/user-management/list']);
  }
}
